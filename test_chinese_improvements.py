#!/usr/bin/env python3
"""
Test script to verify improvements for Chinese site scraping.
Tests the enhanced timeout, viewport, and regex patterns.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scraper import BrowserSession, get_contact_info
from bs4 import BeautifulSoup
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_chinese_site_scraping():
    """Test scraping a Chinese business site with improved settings."""
    
    # Test URLs - using publicly accessible Chinese business sites
    test_urls = [
        "https://www.qichacha.com/",  # Business directory
        "https://www.tianyancha.com/",  # Another business directory
    ]
    
    browser_session = None
    
    try:
        logger.info("Testing improved Chinese site scraping...")
        browser_session = BrowserSession()
        
        for url in test_urls:
            try:
                logger.info(f"Testing URL: {url}")
                
                # Test the improved browser session
                html = browser_session.get(url)
                soup = BeautifulSoup(html, 'html.parser')
                
                # Test contact extraction
                contact_info = get_contact_info(soup, url)
                
                logger.info(f"Results for {url}:")
                for key, value in contact_info.items():
                    logger.info(f"  {key}: {value}")
                
                # Check if we got any meaningful content
                page_text = soup.get_text()[:500].replace('\n', ' ').strip()
                logger.info(f"Page content preview: {page_text[:200]}...")
                
                if len(page_text) > 100:
                    logger.info("✓ Successfully loaded page content")
                else:
                    logger.warning("⚠ Page content seems limited")
                
            except Exception as e:
                logger.error(f"Failed to test {url}: {e}")
                continue
                
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False
    finally:
        if browser_session:
            browser_session.close()
    
    logger.info("Test completed!")
    return True

def test_phone_regex_patterns():
    """Test the improved Chinese phone number regex patterns."""
    
    logger.info("Testing improved phone number patterns...")
    
    # Test phone numbers in various Chinese formats
    test_text = """
    联系电话：138-1234-5678
    手机号码：+86 139 8765 4321
    座机：021-12345678
    客服电话：************
    Tel: 1391234567
    电话: 13912345678
    固话: 010-87654321
    """
    
    from scraper import get_contact_info
    from bs4 import BeautifulSoup
    
    # Create a mock soup object
    soup = BeautifulSoup(f"<html><body>{test_text}</body></html>", 'html.parser')
    
    # Extract contact info
    contact_info = get_contact_info(soup, "test_url")
    
    logger.info("Phone extraction results:")
    logger.info(f"Found phones: {contact_info.get('ScrapedPhone', 'None')}")
    
    # Check if we found any phones
    if contact_info.get('ScrapedPhone', 'N/A') != 'N/A':
        logger.info("✓ Phone pattern matching is working")
        return True
    else:
        logger.warning("⚠ No phones detected - patterns may need adjustment")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Testing Chinese Site Scraping Improvements")
    print("=" * 60)
    
    # Test phone regex patterns first (faster)
    print("\n1. Testing phone number regex patterns...")
    phone_test_passed = test_phone_regex_patterns()
    
    # Test actual site scraping
    print("\n2. Testing Chinese site scraping...")
    site_test_passed = test_chinese_site_scraping()
    
    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"Phone regex test: {'✓ PASSED' if phone_test_passed else '✗ FAILED'}")
    print(f"Site scraping test: {'✓ PASSED' if site_test_passed else '✗ FAILED'}")
    print("=" * 60)
