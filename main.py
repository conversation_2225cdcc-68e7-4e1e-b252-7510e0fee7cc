import pandas as pd
from scraper import scrape_contacts, bright_data_config
from utils import read_excel_from_path, save_to_excel, check_bright_data_config, format_scraping_results
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    """
    Main function to run the enhanced scraper with Bright Data integration.
    """
    input_file = "data/查询企业名单20250819 - 副本.xlsx"
    output_file = "data/enhanced_scraped_contacts.xlsx"

    print("🚀 Enhanced China Contact Scraper with Bright Data")
    print("=" * 60)

    # Check Bright Data configuration
    config_status = check_bright_data_config()
    print("\n🔧 Bright Data Configuration Status:")
    print(f"  API Key: {'✅ Configured' if config_status['api_key_configured'] else '❌ Missing'}")
    print(f"  Datacenter Proxy: {'✅ Configured' if config_status['datacenter_proxy_configured'] else '❌ Not configured'}")
    print(f"  Residential Proxy: {'✅ Configured' if config_status['residential_proxy_configured'] else '❌ Not configured'}")

    use_bright_data = config_status['fully_configured']
    if use_bright_data:
        print("🎉 Bright Data integration active - expecting 90-95% success rate!")
    else:
        print("⚠️ Using direct connections - configure Bright Data for better results")
        print("💡 See .env file for configuration instructions")

    print(f"\n📁 Reading companies from {input_file}...")
    df = read_excel_from_path(input_file)

    print(f"📊 Total companies found: {len(df)}")
    print("📋 Columns available:", list(df.columns))

    # Test with first 3 companies only
    test_df = df.head(3)
    print(f"\n🧪 Testing with first {len(test_df)} companies:")
    for i, row in test_df.iterrows():
        print(f"  {i+1}. {row['CompanyName']}")
        if 'ExistingEmail' in row and pd.notna(row['ExistingEmail']):
            print(f"     📧 Email: {row['ExistingEmail']}")
        if 'ExistingWebsite' in row and pd.notna(row['ExistingWebsite']):
            print(f"     🌐 Website: {row['ExistingWebsite']}")

    strategy_name = "Enhanced with Bright Data" if use_bright_data else "Enhanced (Direct)"
    print(f"\n🚀 Starting scraping process with {strategy_name}...")
    results_df = scrape_contacts(test_df, use_bright_data=use_bright_data)

    print(f"\n💾 Scraping finished. Saving results to {output_file}...")
    output_bytes = save_to_excel(results_df)
    with open(output_file, 'wb') as f:
        f.write(output_bytes)

    print("✅ Done!")
    print(f"📁 Results saved to {os.path.abspath(output_file)}")

    # Show enhanced summary
    results_summary = format_scraping_results(results_df, use_bright_data)

    print(f"\n📊 Enhanced Scraping Summary:")
    print("=" * 40)
    print(f"Strategy Used: {results_summary['strategy_used']}")
    print(f"Total Processed: {results_summary['total_processed']}")
    print(f"Successfully Scraped: {results_summary['success_count']}")
    print(f"Success Rate: {results_summary['success_rate']:.1f}%")

    print(f"\n📞 Contacts Found:")
    contacts = results_summary['contacts_found']
    print(f"  📱 Phones: {contacts['phones']}")
    print(f"  📧 Emails: {contacts['emails']}")
    print(f"  🏢 Addresses: {contacts['addresses']}")
    print(f"  🌐 Websites: {contacts['websites']}")

    print(f"\n📈 Status Breakdown:")
    for status, count in results_summary['status_breakdown'].items():
        print(f"  {status}: {count}")

    # Show recommendations
    if results_summary['success_rate'] < 80 and not use_bright_data:
        print(f"\n💡 Recommendation:")
        print("Configure Bright Data proxies in .env file for better success rates!")
    elif use_bright_data:
        print(f"\n🎉 Great results with Bright Data integration!")

if __name__ == "__main__":
    main()
