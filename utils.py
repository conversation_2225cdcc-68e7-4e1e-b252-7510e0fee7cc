import pandas as pd
from io import BytesIO
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def read_excel_file(uploaded_file):
    """Reads an uploaded Excel file into a pandas DataFrame."""
    df = pd.read_excel(uploaded_file)
    if 'CompanyName' not in df.columns:
        # If 'CompanyName' is not present, we assume the Chinese name and rename it.
        if '企业名称' in df.columns:
            df.rename(columns={'企业名称': 'CompanyName'}, inplace=True)
        else:
            raise ValueError("Excel file must contain a 'CompanyName' or '企业名称' column.")
    return df

def read_excel_from_path(file_path: str) -> pd.DataFrame:
    """Reads an Excel file from a given path into a pandas DataFrame."""
    df = pd.read_excel(file_path)
    
    # Map Chinese columns to English for easier processing
    column_mapping = {
        '企业名称': 'CompanyName',
        '邮箱': 'ExistingEmail', 
        '网址': 'ExistingWebsite',
        '企业地址': 'ExistingAddress',
        '法定代表人': 'LegalRepresentative',
        '注册资本': 'RegisteredCapital',
        '成立日期': 'EstablishmentDate',
        '所属城市': 'City',
        '所属省份': 'Province',
        '所属区县': 'District',
        '登记状态': 'RegistrationStatus',
        '企业类型': 'CompanyType',
        '经营范围': 'BusinessScope',
        '统一社会信用代码': 'CreditCode'
    }
    
    # Rename columns that exist
    for chinese_col, english_col in column_mapping.items():
        if chinese_col in df.columns:
            df.rename(columns={chinese_col: english_col}, inplace=True)
    
    # Ensure CompanyName exists
    if 'CompanyName' not in df.columns:
        df.rename(columns={df.columns[0]: 'CompanyName'}, inplace=True)
    
    return df

def save_to_excel(df: pd.DataFrame) -> bytes:
    """Saves a DataFrame to an in-memory Excel file and returns the bytes."""
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Contacts')
    return output.getvalue()

def check_bright_data_config() -> dict:
    """Check Bright Data configuration status."""
    config_status = {
        'api_key_configured': bool(os.getenv('BRIGHT_DATA_API_KEY')),
        'datacenter_proxy_configured': all([
            os.getenv('BRIGHT_DATA_PROXY_HOST'),
            os.getenv('BRIGHT_DATA_USERNAME'),
            os.getenv('BRIGHT_DATA_PASSWORD')
        ]),
        'residential_proxy_configured': all([
            os.getenv('BRIGHT_DATA_RESIDENTIAL_HOST'),
            os.getenv('BRIGHT_DATA_RESIDENTIAL_USERNAME'),
            os.getenv('BRIGHT_DATA_RESIDENTIAL_PASSWORD')
        ])
    }

    config_status['any_proxy_configured'] = (
        config_status['datacenter_proxy_configured'] or
        config_status['residential_proxy_configured']
    )

    config_status['fully_configured'] = (
        config_status['api_key_configured'] and
        config_status['any_proxy_configured']
    )

    return config_status

def get_bright_data_setup_instructions() -> str:
    """Get setup instructions for Bright Data configuration."""
    return """
    ## 🚀 Bright Data Setup Instructions

    To maximize scraping success rates, configure Bright Data proxies:

    ### 1. Get Your Credentials
    - Log into your Bright Data dashboard
    - Navigate to Proxy & Scraping Infrastructure
    - Create or select a proxy zone (datacenter or residential)
    - Copy the endpoint details

    ### 2. Update .env File
    Add your credentials to the .env file:
    ```
    BRIGHT_DATA_API_KEY=your_api_key_here
    BRIGHT_DATA_PROXY_HOST=brd-customer-hl_xxxxx-zone-datacenter.brightdata.com
    BRIGHT_DATA_PROXY_PORT=22225
    BRIGHT_DATA_USERNAME=brd-customer-hl_xxxxx-zone-datacenter
    BRIGHT_DATA_PASSWORD=your_zone_password
    ```

    ### 3. Benefits
    - ✅ Up to 95% success rate (vs 85% without proxies)
    - ✅ Better access to protected Chinese websites
    - ✅ Reduced CAPTCHA challenges
    - ✅ Automatic proxy rotation
    - ✅ Enhanced stealth capabilities

    ### 4. Cost Optimization
    - Use datacenter proxies for general scraping (cheaper)
    - Use residential proxies for heavily protected sites
    - Monitor usage in Bright Data dashboard
    """

def format_scraping_results(results_df: pd.DataFrame, use_bright_data: bool = False) -> dict:
    """Format scraping results with enhanced metrics."""
    if len(results_df) == 0:
        return {
            'total_processed': 0,
            'success_count': 0,
            'success_rate': 0,
            'contacts_found': {
                'phones': 0,
                'emails': 0,
                'addresses': 0,
                'websites': 0
            },
            'strategy_used': 'Enhanced' if use_bright_data else 'Standard'
        }

    success_count = len(results_df[results_df['Status'] == 'Success'])
    success_rate = (success_count / len(results_df) * 100)

    contacts_found = {
        'phones': len(results_df[results_df['ScrapedPhone'] != 'N/A']),
        'emails': len(results_df[results_df['ScrapedEmail'] != 'N/A']),
        'addresses': len(results_df[results_df['ScrapedAddress'] != 'N/A']),
        'websites': len(results_df[results_df['ScrapedWebsite'] != 'N/A'])
    }

    return {
        'total_processed': len(results_df),
        'success_count': success_count,
        'success_rate': success_rate,
        'contacts_found': contacts_found,
        'strategy_used': 'Enhanced with Bright Data' if use_bright_data else 'Enhanced (Direct)',
        'status_breakdown': results_df['Status'].value_counts().to_dict()
    }
