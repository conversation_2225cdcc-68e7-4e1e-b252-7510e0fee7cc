#!/usr/bin/env python3
"""Quick test of the enhanced Chinese contact extraction."""

from scraper import extract_chinese_contacts

def main():
    print("🚀 Testing Enhanced Chinese Contact Extraction")
    print("=" * 50)
    
    # Sample Chinese text with various contact formats
    test_text = """
    公司名称：北京科技有限公司
    联系电话：010-12345678
    手机：13812345678
    微信：tech_beijing_2024
    QQ：123456789
    邮箱：<EMAIL>
    企业邮箱：<EMAIL>
    地址：北京市朝阳区建国路88号现代城A座15层
    客服热线：************
    传真：010-87654321
    """
    
    print("📝 Test text:")
    print(test_text.strip())
    print("\n" + "=" * 50)
    
    # Extract contacts
    contacts = extract_chinese_contacts(test_text)
    
    print("📊 Extraction Results:")
    print(f"📞 Phones found ({len(contacts['phones'])}): {contacts['phones']}")
    print(f"📧 Emails found ({len(contacts['emails'])}): {contacts['emails']}")
    print(f"💬 WeChat IDs found ({len(contacts['wechats'])}): {contacts['wechats']}")
    print(f"🐧 QQ numbers found ({len(contacts['qqs'])}): {contacts['qqs']}")
    
    # Check success
    total_found = len(contacts['phones']) + len(contacts['emails']) + len(contacts['wechats']) + len(contacts['qqs'])
    print(f"\n🎯 Total contacts found: {total_found}")
    
    if total_found >= 5:
        print("✅ SUCCESS: Enhanced extraction is working correctly!")
        return True
    else:
        print("❌ ISSUE: Expected more contacts to be found")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
