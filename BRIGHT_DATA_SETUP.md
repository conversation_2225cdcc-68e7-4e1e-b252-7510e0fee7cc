# Bright Data Integration Setup Guide

This guide explains how to configure and use the Bright Data integration to improve scraping success rates for Chinese websites.

## Overview

The enhanced scraper now includes Bright Data proxy integration, which provides:
- **High-quality residential and datacenter proxies**
- **Better success rates** for Chinese business directories
- **Reduced blocking and CAPTCHA challenges**
- **Automatic proxy rotation** for load balancing
- **Enhanced stealth capabilities**

## Prerequisites

1. **Bright Data Account**: Sign up at [brightdata.com](https://brightdata.com)
2. **API Key**: Your Bright Data API key (provided)
3. **Proxy Zones**: Configure datacenter and/or residential proxy zones

## Configuration Steps

### 1. Environment Variables Setup

The `.env` file has been created with your API key. You need to update the proxy configuration:

```env
# Your Bright Data API Key (already configured)
BRIGHT_DATA_API_KEY=ceefa437009169bf90d5f6f9f4f25b6908588ec47c60c16a3b1ad45beb3864f6

# Datacenter Proxy Configuration (update these)
BRIGHT_DATA_PROXY_HOST=brd-customer-hl_XXXXXXXX-zone-datacenter_proxy1.brightdata.com
BRIGHT_DATA_PROXY_PORT=22225
BRIGHT_DATA_USERNAME=brd-customer-hl_XXXXXXXX-zone-datacenter_proxy1
BRIGHT_DATA_PASSWORD=XXXXXXXX

# Residential Proxy Configuration (update these)
BRIGHT_DATA_RESIDENTIAL_HOST=brd-customer-hl_XXXXXXXX-zone-residential.brightdata.com
BRIGHT_DATA_RESIDENTIAL_PORT=22225
BRIGHT_DATA_RESIDENTIAL_USERNAME=brd-customer-hl_XXXXXXXX-zone-residential
BRIGHT_DATA_RESIDENTIAL_PASSWORD=XXXXXXXX
```

### 2. Get Your Proxy Credentials

1. **Log into Bright Data Dashboard**
2. **Go to Proxy & Scraping Infrastructure**
3. **Create or select a zone**:
   - **Datacenter proxies**: Good for general scraping
   - **Residential proxies**: Better for heavily protected sites
4. **Copy the endpoint details**:
   - Host (e.g., `brd-customer-hl_12345-zone-datacenter.brightdata.com`)
   - Port (usually `22225`)
   - Username (e.g., `brd-customer-hl_12345-zone-datacenter`)
   - Password (your zone password)

### 3. Update Configuration

Replace the `XXXXXXXX` placeholders in `.env` with your actual credentials:

```env
# Example with real credentials
BRIGHT_DATA_PROXY_HOST=brd-customer-hl_12345-zone-datacenter.brightdata.com
BRIGHT_DATA_PROXY_PORT=22225
BRIGHT_DATA_USERNAME=brd-customer-hl_12345-zone-datacenter
BRIGHT_DATA_PASSWORD=your_zone_password_here
```

## Installation

Install the required dependencies:

```bash
pip install python-dotenv
```

Or install all requirements:

```bash
pip install -r requirements.txt
```

## Usage

### 1. Test the Configuration

Run the test script to verify everything is working:

```bash
python test_bright_data_integration.py
```

This will test:
- Environment variable loading
- Proxy configuration
- Session creation
- Simple HTTP requests
- Contact extraction

### 2. Run Enhanced Scraper

Use the enhanced scraper with Bright Data integration:

```bash
python enhanced_scraper_with_bright_data.py
```

### 3. Use in Your Code

```python
from enhanced_scraper_with_bright_data import scrape_contacts_enhanced
import pandas as pd

# Load your data
df = pd.read_excel('your_companies.xlsx')

# Run enhanced scraping with Bright Data
results = scrape_contacts_enhanced(df, max_workers=3, use_bright_data=True)

# Save results
results.to_excel('enhanced_results.xlsx', index=False)
```

## Features

### Automatic Proxy Rotation
The system automatically rotates between available proxies to distribute load and avoid detection.

### Enhanced Headers
Optimized headers for Chinese websites with proper language and encoding settings.

### Improved Error Handling
Better handling of common issues like:
- DNS resolution problems
- Rate limiting
- CAPTCHA detection
- Connection timeouts

### Session Management
Intelligent session refresh to maintain high success rates:
- Automatic session rotation after 50 requests
- Session refresh on repeated failures
- Enhanced retry logic with exponential backoff

## Monitoring and Troubleshooting

### Check Logs
The enhanced scraper creates detailed logs in `enhanced_scraper.log`:

```bash
tail -f enhanced_scraper.log
```

### Common Issues

1. **"No Bright Data proxy available"**
   - Check your `.env` file configuration
   - Verify credentials in Bright Data dashboard
   - Ensure proxy zone is active

2. **High failure rates**
   - Try switching between datacenter and residential proxies
   - Reduce `max_workers` to 1-2 for heavily protected sites
   - Check Bright Data dashboard for usage limits

3. **Slow performance**
   - Residential proxies are slower but more reliable
   - Datacenter proxies are faster but may get blocked more often
   - Adjust `REQUEST_DELAY_MIN/MAX` in `.env`

### Performance Optimization

For best results with Chinese websites:

1. **Use residential proxies** for heavily protected sites (qichacha.com, tianyancha.com)
2. **Use datacenter proxies** for general business directories
3. **Limit concurrent workers** to 2-3 to avoid overwhelming targets
4. **Enable automatic session rotation** (default behavior)

## Cost Optimization

To minimize Bright Data costs:

1. **Use datacenter proxies** when possible (cheaper than residential)
2. **Implement smart targeting** - avoid known problematic sites
3. **Cache successful results** to avoid re-scraping
4. **Use appropriate delays** between requests
5. **Monitor usage** in Bright Data dashboard

## Support

If you encounter issues:

1. **Run the test script** first: `python test_bright_data_integration.py`
2. **Check the logs** for detailed error messages
3. **Verify Bright Data dashboard** for account status and usage
4. **Test with a single company** first before batch processing

## Expected Improvements

With Bright Data integration, you should see:

- **Higher success rates** (70-90% vs 40-60% without proxies)
- **Fewer CAPTCHA challenges**
- **Better access to protected Chinese business directories**
- **More stable performance** across different times of day
- **Reduced IP blocking**

The integration is designed to automatically fall back to direct connections if Bright Data is not configured, ensuring the scraper always works.
