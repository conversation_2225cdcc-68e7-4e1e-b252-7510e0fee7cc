#!/usr/bin/env python3
"""Test imports to identify the issue."""

try:
    print("Testing dotenv import...")
    from dotenv import load_dotenv
    print("✅ dotenv import successful")
    
    print("Loading environment variables...")
    load_dotenv()
    print("✅ Environment variables loaded")
    
    print("Testing scraper import...")
    from scraper import scrape_contacts, bright_data_config
    print("✅ scraper imports successful")
    
    print("Testing bright_data_config...")
    print(f"API Key configured: {bool(bright_data_config.api_key)}")
    print(f"Proxy config available: {bright_data_config.has_proxy_config}")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
