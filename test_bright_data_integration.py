"""
Test script for Bright Data integration
Tests the configuration and basic functionality of the enhanced scraper.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment_variables():
    """Test if environment variables are properly loaded."""
    logger.info("🧪 Testing environment variables...")
    
    api_key = os.getenv('BRIGHT_DATA_API_KEY')
    if api_key:
        logger.info(f"✅ BRIGHT_DATA_API_KEY found: {api_key[:10]}...{api_key[-10:]}")
    else:
        logger.warning("❌ BRIGHT_DATA_API_KEY not found")
    
    # Test proxy configuration
    proxy_host = os.getenv('BRIGHT_DATA_PROXY_HOST')
    proxy_port = os.getenv('BRIGHT_DATA_PROXY_PORT')
    proxy_username = os.getenv('BRIGHT_DATA_USERNAME')
    proxy_password = os.getenv('BRIGHT_DATA_PASSWORD')
    
    if all([proxy_host, proxy_port, proxy_username, proxy_password]):
        logger.info("✅ Datacenter proxy configuration found")
    else:
        logger.warning("❌ Incomplete datacenter proxy configuration")
        logger.info(f"  Host: {'✅' if proxy_host else '❌'}")
        logger.info(f"  Port: {'✅' if proxy_port else '❌'}")
        logger.info(f"  Username: {'✅' if proxy_username else '❌'}")
        logger.info(f"  Password: {'✅' if proxy_password else '❌'}")
    
    return bool(api_key)

def test_bright_data_config():
    """Test Bright Data configuration module."""
    logger.info("🧪 Testing Bright Data configuration...")
    
    try:
        from bright_data_config import bright_data_config, get_bright_data_headers
        
        logger.info(f"✅ Configuration loaded successfully")
        logger.info(f"Has proxy config: {'✅' if bright_data_config.has_proxy_config else '❌'}")
        
        # Test proxy configurations
        datacenter_proxy = bright_data_config.get_datacenter_proxy()
        if datacenter_proxy:
            logger.info("✅ Datacenter proxy configuration available")
        else:
            logger.warning("❌ Datacenter proxy configuration not available")
        
        residential_proxy = bright_data_config.get_residential_proxy()
        if residential_proxy:
            logger.info("✅ Residential proxy configuration available")
        else:
            logger.warning("❌ Residential proxy configuration not available")
        
        # Test headers
        headers = get_bright_data_headers()
        logger.info(f"✅ Enhanced headers generated: {len(headers)} headers")
        logger.info(f"  User-Agent: {headers.get('User-Agent', 'N/A')[:50]}...")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Failed to import Bright Data configuration: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Error testing Bright Data configuration: {e}")
        return False

def test_enhanced_session():
    """Test enhanced scraping session."""
    logger.info("🧪 Testing enhanced scraping session...")
    
    try:
        from enhanced_scraper_with_bright_data import EnhancedScrapingSession
        
        # Test with Bright Data
        session_with_proxy = EnhancedScrapingSession(use_bright_data=True)
        logger.info("✅ Enhanced session with Bright Data created successfully")
        
        # Test without Bright Data
        session_without_proxy = EnhancedScrapingSession(use_bright_data=False)
        logger.info("✅ Enhanced session without Bright Data created successfully")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Failed to import enhanced scraper: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Error testing enhanced session: {e}")
        return False

def test_simple_request():
    """Test a simple request with the enhanced session."""
    logger.info("🧪 Testing simple request...")
    
    try:
        from enhanced_scraper_with_bright_data import EnhancedScrapingSession
        
        session = EnhancedScrapingSession(use_bright_data=True)
        
        # Test with a simple, reliable website
        test_url = "https://httpbin.org/headers"
        
        logger.info(f"Making test request to: {test_url}")
        response = session.get(test_url, timeout=30)
        
        if response.status_code == 200:
            logger.info("✅ Test request successful")
            logger.info(f"Response length: {len(response.text)} characters")
            return True
        else:
            logger.warning(f"❌ Test request failed with status: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test request failed: {e}")
        return False

def test_contact_extraction():
    """Test contact extraction functionality."""
    logger.info("🧪 Testing contact extraction...")
    
    try:
        from enhanced_scraper_with_bright_data import extract_chinese_contacts_enhanced
        
        # Test text with various contact formats
        test_text = """
        公司名称：测试科技有限公司
        联系电话：13812345678
        座机：021-12345678
        邮箱：<EMAIL>
        微信：testweixin123
        QQ：123456789
        地址：上海市浦东新区张江高科技园区
        """
        
        contacts = extract_chinese_contacts_enhanced(test_text)
        
        logger.info("✅ Contact extraction completed")
        logger.info(f"  Phones found: {len(contacts['phones'])} - {list(contacts['phones'])}")
        logger.info(f"  Emails found: {len(contacts['emails'])} - {list(contacts['emails'])}")
        logger.info(f"  WeChat found: {len(contacts['wechats'])} - {list(contacts['wechats'])}")
        logger.info(f"  QQ found: {len(contacts['qqs'])} - {list(contacts['qqs'])}")
        
        # Check if we found expected contacts
        expected_phone = '13812345678'
        expected_email = '<EMAIL>'
        
        if expected_phone in contacts['phones']:
            logger.info(f"✅ Expected phone number found: {expected_phone}")
        else:
            logger.warning(f"❌ Expected phone number not found: {expected_phone}")
        
        if expected_email in contacts['emails']:
            logger.info(f"✅ Expected email found: {expected_email}")
        else:
            logger.warning(f"❌ Expected email not found: {expected_email}")
        
        return len(contacts['phones']) > 0 or len(contacts['emails']) > 0
        
    except Exception as e:
        logger.error(f"❌ Contact extraction test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting Bright Data integration tests...")
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Bright Data Configuration", test_bright_data_config),
        ("Enhanced Session", test_enhanced_session),
        ("Simple Request", test_simple_request),
        ("Contact Extraction", test_contact_extraction),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.warning(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All tests passed! Bright Data integration is ready.")
    else:
        logger.warning("⚠️ Some tests failed. Please check the configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
