"""
Bulletproof Chinese Company Contact Scraper
Focuses on accessible sources and avoids heavily protected sites like Qichacha/Tianyancha
"""

import requests
from bs4 import BeautifulSoup
import re
import time
import random
import urllib.parse
import logging
from typing import Dict, List, Optional
import pandas as pd
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReliableChineseScraper:
    """Bulletproof scraper that focuses on accessible Chinese business sources."""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """Setup session with Chinese-optimized headers."""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'https://www.baidu.com/',
        })
        
        # Setup retry strategy
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def get_company_contacts(self, company_name: str) -> Dict[str, str]:
        """Main method to get company contacts using bulletproof strategy."""
        logger.info(f"🔍 Searching for contacts: {company_name}")
        
        results = {
            'ScrapedPhone': 'N/A',
            'ScrapedEmail': 'N/A', 
            'ScrapedAddress': 'N/A',
            'ScrapedWebsite': 'N/A',
            'Status': 'Failed'
        }
        
        # Strategy 1: Government sources (most reliable)
        gov_results = self.try_government_sources(company_name)
        if gov_results:
            results.update(gov_results)
            if results['ScrapedPhone'] != 'N/A' or results['ScrapedEmail'] != 'N/A':
                results['Status'] = 'Success'
                return results
        
        # Strategy 2: Accessible business directories
        dir_results = self.try_accessible_directories(company_name)
        if dir_results:
            results.update(dir_results)
            if results['ScrapedPhone'] != 'N/A' or results['ScrapedEmail'] != 'N/A':
                results['Status'] = 'Success'
                return results
        
        # Strategy 3: Search engine indirect approach
        search_results = self.try_search_engines(company_name)
        if search_results:
            results.update(search_results)
            if results['ScrapedPhone'] != 'N/A' or results['ScrapedEmail'] != 'N/A':
                results['Status'] = 'Success'
                return results
        
        return results
    
    def try_government_sources(self, company_name: str) -> Dict[str, str]:
        """Try government and official sources."""
        logger.info(f"🏛️ Trying government sources for {company_name}")
        
        sources = [
            ('GSXT', f'https://www.gsxt.gov.cn/corp-query-search-1.html?keyword={urllib.parse.quote(company_name)}'),
            ('Credit China', f'https://www.creditchina.gov.cn/search?keyword={urllib.parse.quote(company_name)}'),
        ]
        
        for source_name, url in sources:
            try:
                logger.info(f"Trying {source_name}...")
                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    contacts = self.extract_contacts_from_content(soup.get_text())
                    if contacts['phones'] or contacts['emails']:
                        return self.format_results(contacts)
                time.sleep(2)
            except Exception as e:
                logger.warning(f"{source_name} failed: {e}")
                continue
        
        return {}
    
    def try_accessible_directories(self, company_name: str) -> Dict[str, str]:
        """Try accessible business directories."""
        logger.info(f"📋 Trying accessible directories for {company_name}")
        
        directories = [
            ('Huangye88', f'https://www.huangye88.com/search/{urllib.parse.quote(company_name)}'),
            ('B2B.cn', f'https://www.b2b.cn/search/{urllib.parse.quote(company_name)}'),
            ('Made-in-China', f'https://www.made-in-china.com/search?word={urllib.parse.quote(company_name)}'),
            ('1688', f'https://www.1688.com/company/company_search.htm?keywords={urllib.parse.quote(company_name)}'),
        ]
        
        for dir_name, url in directories:
            try:
                logger.info(f"Trying {dir_name}...")
                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    contacts = self.extract_contacts_from_content(soup.get_text())
                    if contacts['phones'] or contacts['emails']:
                        return self.format_results(contacts)
                time.sleep(3)
            except Exception as e:
                logger.warning(f"{dir_name} failed: {e}")
                continue
        
        return {}
    
    def try_search_engines(self, company_name: str) -> Dict[str, str]:
        """Try search engines for indirect contact discovery."""
        logger.info(f"🔍 Trying search engines for {company_name}")
        
        search_queries = [
            f'{company_name} 联系方式',
            f'{company_name} 电话 邮箱',
            f'{company_name} 官网',
        ]
        
        search_engines = [
            ('Baidu', 'https://www.baidu.com/s?wd={}'),
            ('Sogou', 'https://www.sogou.com/web?query={}'),
        ]
        
        for query in search_queries[:2]:  # Limit queries
            for engine_name, search_template in search_engines:
                try:
                    search_url = search_template.format(urllib.parse.quote(query))
                    logger.info(f"Searching {engine_name}: {query}")
                    
                    response = self.session.get(search_url, timeout=30)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # Extract contact info from search results
                        contacts = self.extract_contacts_from_content(soup.get_text())
                        if contacts['phones'] or contacts['emails']:
                            return self.format_results(contacts)
                    
                    time.sleep(2)
                except Exception as e:
                    logger.warning(f"{engine_name} search failed: {e}")
                    continue
        
        return {}
    
    def extract_contacts_from_content(self, text: str) -> Dict[str, set]:
        """Extract contacts from text content."""
        contacts = {"phones": set(), "emails": set(), "addresses": set()}
        
        # Enhanced Chinese phone patterns
        phone_patterns = [
            r'1[3-9]\d{9}',  # Mobile phones
            r'0\d{2,3}-?\d{7,8}',  # Landlines
            r'400-?\d{3}-?\d{4}',  # 400 numbers
            r'(\+?86\s?)?1[3-9]\d{9}',  # Mobile with country code
        ]
        
        # Email patterns
        email_patterns = [
            r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
        ]
        
        # Address patterns
        address_patterns = [
            r'地址[：:\s]*([^\n]{10,100})',
            r'联系地址[：:\s]*([^\n]{10,100})',
            r'公司地址[：:\s]*([^\n]{10,100})',
        ]
        
        # Extract phones
        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                phone = re.sub(r'[-\s+()（）]', '', str(match))
                if phone.startswith('86') and len(phone) > 11:
                    phone = phone[2:]
                if len(phone) >= 7 and phone.isdigit():
                    contacts["phones"].add(phone)
        
        # Extract emails
        for pattern in email_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                email = str(match).strip()
                if '@' in email and '.' in email and len(email) > 5:
                    # Filter out invalid domains
                    skip_domains = ['example.com', 'test.com', 'noreply', 'localhost']
                    if not any(skip in email.lower() for skip in skip_domains):
                        contacts["emails"].add(email)
        
        # Extract addresses
        for pattern in address_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                address = str(match).strip()
                if len(address) > 10 and any(c in address for c in ['市', '区', '路', '街', '号']):
                    contacts["addresses"].add(address[:100])
        
        return contacts
    
    def format_results(self, contacts: Dict[str, set]) -> Dict[str, str]:
        """Format extracted contacts into result dictionary."""
        results = {}
        
        if contacts['phones']:
            results['ScrapedPhone'] = ', '.join(sorted(contacts['phones']))
        
        if contacts['emails']:
            results['ScrapedEmail'] = ', '.join(sorted(contacts['emails']))
        
        if contacts['addresses']:
            results['ScrapedAddress'] = list(contacts['addresses'])[0]  # Take first address
        
        return results

def scrape_companies_reliable(df: pd.DataFrame, max_workers: int = 2) -> pd.DataFrame:
    """Scrape companies using the reliable strategy."""
    scraper = ReliableChineseScraper()
    results = []
    
    def scrape_single_company(row):
        company_name = row.get('CompanyName') or row.get('企业名称', '')
        if not company_name:
            return {
                'CompanyName': 'Unknown',
                'ScrapedPhone': 'N/A',
                'ScrapedEmail': 'N/A',
                'ScrapedAddress': 'N/A',
                'ScrapedWebsite': 'N/A',
                'Status': 'No company name'
            }
        
        result = scraper.get_company_contacts(company_name)
        result['CompanyName'] = company_name
        return result
    
    # Use ThreadPoolExecutor for concurrent processing
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_row = {executor.submit(scrape_single_company, row): row for _, row in df.iterrows()}
        
        for future in as_completed(future_to_row):
            try:
                result = future.result()
                results.append(result)
                logger.info(f"✅ Completed: {result['CompanyName']} - {result['Status']}")
            except Exception as e:
                logger.error(f"❌ Error processing company: {e}")
                results.append({
                    'CompanyName': 'Error',
                    'ScrapedPhone': 'N/A',
                    'ScrapedEmail': 'N/A',
                    'ScrapedAddress': 'N/A',
                    'ScrapedWebsite': 'N/A',
                    'Status': f'Error: {str(e)}'
                })
    
    return pd.DataFrame(results)

if __name__ == "__main__":
    # Test with sample data
    test_data = pd.DataFrame({
        'CompanyName': ['华为技术有限公司', '腾讯科技有限公司', '阿里巴巴集团']
    })
    
    results = scrape_companies_reliable(test_data, max_workers=1)
    print(results)
