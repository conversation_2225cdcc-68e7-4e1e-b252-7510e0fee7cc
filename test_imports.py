#!/usr/bin/env python3
"""Test all imports to identify the issue."""

import sys
print(f"Python version: {sys.version}")
print(f"Python path: {sys.executable}")
print("=" * 50)

try:
    import pandas as pd
    print("✅ pandas imported successfully")
except ImportError as e:
    print(f"❌ pandas import failed: {e}")

try:
    import requests
    print("✅ requests imported successfully")
except ImportError as e:
    print(f"❌ requests import failed: {e}")

try:
    from bs4 import BeautifulSoup
    print("✅ BeautifulSoup imported successfully")
except ImportError as e:
    print(f"❌ BeautifulSoup import failed: {e}")

try:
    import time
    print("✅ time imported successfully")
except ImportError as e:
    print(f"❌ time import failed: {e}")

try:
    import random
    print("✅ random imported successfully")
except ImportError as e:
    print(f"❌ random import failed: {e}")

try:
    import re
    print("✅ re imported successfully")
except ImportError as e:
    print(f"❌ re import failed: {e}")

try:
    from typing import Dict, List, Optional, Tuple
    print("✅ typing imported successfully")
except ImportError as e:
    print(f"❌ typing import failed: {e}")

try:
    from pandas import DataFrame
    print("✅ DataFrame imported successfully")
except ImportError as e:
    print(f"❌ DataFrame import failed: {e}")

try:
    import os
    print("✅ os imported successfully")
except ImportError as e:
    print(f"❌ os import failed: {e}")

try:
    from urllib.parse import urlparse
    print("✅ urlparse imported successfully")
except ImportError as e:
    print(f"❌ urlparse import failed: {e}")

try:
    from fake_useragent import UserAgent
    print("✅ UserAgent imported successfully")
except ImportError as e:
    print(f"❌ UserAgent import failed: {e}")

try:
    import logging
    print("✅ logging imported successfully")
except ImportError as e:
    print(f"❌ logging import failed: {e}")

try:
    from concurrent.futures import ThreadPoolExecutor, as_completed
    print("✅ ThreadPoolExecutor imported successfully")
except ImportError as e:
    print(f"❌ ThreadPoolExecutor import failed: {e}")

try:
    from playwright.sync_api import sync_playwright
    print("✅ playwright imported successfully")
except ImportError as e:
    print(f"❌ playwright import failed: {e}")

try:
    import chardet
    print(f"✅ chardet imported successfully (version: {chardet.__version__})")
except ImportError as e:
    print(f"❌ chardet import failed: {e}")

try:
    import urllib.parse
    print("✅ urllib.parse imported successfully")
except ImportError as e:
    print(f"❌ urllib.parse import failed: {e}")

print("=" * 50)
print("Testing scraper module import...")

try:
    from scraper import extract_chinese_contacts
    print("✅ extract_chinese_contacts imported successfully")
except ImportError as e:
    print(f"❌ extract_chinese_contacts import failed: {e}")

try:
    from scraper import BrowserSession
    print("✅ BrowserSession imported successfully")
except ImportError as e:
    print(f"❌ BrowserSession import failed: {e}")

try:
    from scraper import ScrapingSession
    print("✅ ScrapingSession imported successfully")
except ImportError as e:
    print(f"❌ ScrapingSession import failed: {e}")

try:
    from scraper import scrape_contacts
    print("✅ scrape_contacts imported successfully")
except ImportError as e:
    print(f"❌ scrape_contacts import failed: {e}")

print("=" * 50)
print("All import tests completed!")
