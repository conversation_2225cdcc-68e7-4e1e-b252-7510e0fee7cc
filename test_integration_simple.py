"""
Simple test script to verify Bright Data integration works.
"""

import os
import sys
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        from scraper import scrape_contacts, bright_data_config
        print("SUCCESS: scraper module imported")
        
        from utils import check_bright_data_config, format_scraping_results
        print("SUCCESS: utils module imported")
        
        # Test Bright Data config
        print(f"Bright Data config loaded: {bright_data_config.has_proxy_config}")
        
        return True
    except Exception as e:
        print(f"FAILED: Import failed: {e}")
        return False

def test_bright_data_config():
    """Test Bright Data configuration."""
    print("\nTesting Bright Data configuration...")
    
    try:
        from utils import check_bright_data_config
        
        config_status = check_bright_data_config()
        
        print(f"API Key configured: {config_status['api_key_configured']}")
        print(f"Datacenter proxy: {config_status['datacenter_proxy_configured']}")
        print(f"Residential proxy: {config_status['residential_proxy_configured']}")
        print(f"Fully configured: {config_status['fully_configured']}")
        
        return True
    except Exception as e:
        print(f"FAILED: Configuration test failed: {e}")
        return False

def test_streamlit_compatibility():
    """Test that the app components work for Streamlit."""
    print("\nTesting Streamlit compatibility...")
    
    try:
        # Test utils functions
        from utils import save_to_excel
        
        test_df = pd.DataFrame({
            'CompanyName': ['Test Company'],
            'Status': ['Success'],
            'ScrapedPhone': ['13812345678'],
            'ScrapedEmail': ['<EMAIL>']
        })
        
        excel_bytes = save_to_excel(test_df)
        print(f"SUCCESS: Excel export works: {len(excel_bytes)} bytes")
        
        return True
        
    except Exception as e:
        print(f"FAILED: Streamlit compatibility test failed: {e}")
        return False

def show_configuration_status():
    """Show current configuration status."""
    print("\nBright Data Configuration Status")
    print("=" * 40)
    
    api_key = os.getenv('BRIGHT_DATA_API_KEY')
    if api_key:
        print(f"API Key found: {api_key[:10]}...{api_key[-6:]}")
    else:
        print("API Key: Not found")
    
    # Check proxy configuration
    proxy_configs = [
        ("Datacenter Host", "BRIGHT_DATA_PROXY_HOST"),
        ("Datacenter Username", "BRIGHT_DATA_USERNAME"),
        ("Datacenter Password", "BRIGHT_DATA_PASSWORD"),
        ("Residential Host", "BRIGHT_DATA_RESIDENTIAL_HOST"),
        ("Residential Username", "BRIGHT_DATA_RESIDENTIAL_USERNAME"),
        ("Residential Password", "BRIGHT_DATA_RESIDENTIAL_PASSWORD"),
    ]
    
    for name, env_var in proxy_configs:
        value = os.getenv(env_var)
        if value and value != "XXXXXXXX":
            if "Password" in name:
                print(f"{name}: Configured")
            else:
                print(f"{name}: {value}")
        else:
            print(f"{name}: Not configured")

def main():
    """Run all tests."""
    print("Bright Data Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Bright Data Configuration", test_bright_data_config),
        ("Streamlit Compatibility", test_streamlit_compatibility),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-'*30}")
        print(f"Running: {test_name}")
        print(f"{'-'*30}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"RESULT: {test_name} PASSED")
            else:
                print(f"RESULT: {test_name} FAILED")
                
        except Exception as e:
            print(f"ERROR: {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("SUCCESS: All tests passed! Your enhanced scraper is ready.")
        print("To start the web interface, run: streamlit run app.py")
    else:
        print("WARNING: Some tests failed. Check the configuration.")
    
    # Show configuration status
    show_configuration_status()
    
    print("\nConfiguration Instructions:")
    print("1. Your Bright Data API key is already configured")
    print("2. To add proxy support, update .env file with your proxy credentials")
    print("3. Get credentials from your Bright Data dashboard")
    print("4. Uncomment and update the proxy lines in .env file")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
