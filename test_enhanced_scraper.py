#!/usr/bin/env python3
"""
Test script for the enhanced Chinese website scraper.
Demonstrates the advanced anti-detection techniques.
"""

import asyncio
import logging
from scraper import (
    BrowserSession, 
    ScrapingSession, 
    extract_chinese_contacts,
    advanced_china_scraper,
    get_contact_info
)
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_chinese_contact_extraction():
    """Test the enhanced Chinese contact extraction patterns."""
    print("\n=== Testing Chinese Contact Extraction ===")
    
    # Sample Chinese text with various contact formats
    test_text = """
    公司名称：北京科技有限公司
    联系电话：010-12345678
    手机：13812345678
    微信：tech_beijing_2024
    QQ：123456789
    邮箱：<EMAIL>
    企业邮箱：<EMAIL>
    地址：北京市朝阳区建国路88号现代城A座15层
    客服热线：************
    传真：010-87654321
    """
    
    contacts = extract_chinese_contacts(test_text)
    
    print(f"📞 Phones found: {contacts['phones']}")
    print(f"📧 Emails found: {contacts['emails']}")
    print(f"💬 WeChat IDs found: {contacts['wechats']}")
    print(f"🐧 QQ numbers found: {contacts['qqs']}")
    
    return len(contacts['phones']) > 0 or len(contacts['emails']) > 0

def test_stealth_browser():
    """Test the stealth browser configuration."""
    print("\n=== Testing Stealth Browser Session ===")
    
    try:
        session = BrowserSession()
        if session.playwright_available:
            print("✅ Stealth browser session created successfully")
            print(f"   - User agents available: {len(session.CHINESE_USER_AGENTS)}")
            print(f"   - Rate limiting: {session.min_delay}-{session.max_delay}s")
            session.close()
            return True
        else:
            print("❌ Playwright not available")
            return False
    except Exception as e:
        print(f"❌ Stealth browser test failed: {e}")
        return False

def test_enhanced_session():
    """Test the enhanced HTTP session."""
    print("\n=== Testing Enhanced HTTP Session ===")
    
    try:
        session = ScrapingSession()
        print("✅ Enhanced HTTP session created successfully")
        print(f"   - Chinese user agents: {len(session.CHINESE_USER_AGENTS)}")
        print(f"   - Headers configured: {len(session.session.headers)}")
        print(f"   - Rate limiting: {session.min_delay}-{session.max_delay}s")
        
        # Test headers
        headers = session.session.headers
        required_headers = ['Accept-Language', 'Referer', 'DNT']
        missing_headers = [h for h in required_headers if h not in headers]
        
        if not missing_headers:
            print("✅ All required Chinese-optimized headers present")
            return True
        else:
            print(f"❌ Missing headers: {missing_headers}")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced session test failed: {e}")
        return False

def test_advanced_scraper():
    """Test the advanced China scraper with a safe URL."""
    print("\n=== Testing Advanced China Scraper ===")
    
    # Use a safe test URL (example.com with Chinese content simulation)
    test_url = "https://httpbin.org/html"
    
    try:
        result = advanced_china_scraper(test_url)
        if result:
            print("✅ Advanced scraper executed successfully")
            print(f"   - Phone: {result.get('ScrapedPhone', 'N/A')}")
            print(f"   - Email: {result.get('ScrapedEmail', 'N/A')}")
            print(f"   - Address: {result.get('ScrapedAddress', 'N/A')}")
            return True
        else:
            print("❌ Advanced scraper returned no results")
            return False
    except Exception as e:
        print(f"❌ Advanced scraper test failed: {e}")
        return False

def run_all_tests():
    """Run all enhancement tests."""
    print("🚀 Testing Enhanced Chinese Website Scraper")
    print("=" * 50)
    
    tests = [
        ("Chinese Contact Extraction", test_chinese_contact_extraction),
        ("Stealth Browser Session", test_stealth_browser),
        ("Enhanced HTTP Session", test_enhanced_session),
        ("Advanced China Scraper", test_advanced_scraper),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All enhancements are working correctly!")
    else:
        print("⚠️  Some enhancements need attention.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
