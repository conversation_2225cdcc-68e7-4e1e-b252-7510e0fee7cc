#!/usr/bin/env python3
"""
Quick test to validate DNS improvements and enhanced scraper.
"""

import pandas as pd
import time
from scraper import scrape_contacts, robust_china_scraper
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_test():
    """Run a quick test with a few companies to validate improvements."""
    print("🚀 Quick Test: Enhanced Scraper with DNS Fixes")
    print("="*50)
    
    # Test with 5 companies for quick validation
    test_companies = [
        {'CompanyName': '阿里巴巴集团控股有限公司', 'City': '杭州', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '腾讯控股有限公司', 'City': '深圳', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '华为技术有限公司', 'City': '深圳', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '小米科技有限责任公司', 'City': '北京', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '百度在线网络技术（北京）有限公司', 'City': '北京', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
    ]
    
    test_df = pd.DataFrame(test_companies)
    print(f"📋 Testing with {len(test_df)} companies")
    
    # Record start time
    start_time = time.time()
    
    try:
        # Run enhanced scraper
        print("🔍 Running enhanced scraper...")
        results_df = scrape_contacts(test_df, max_workers=2)
        
        # Record end time
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  Completed in {duration:.1f} seconds")
        
        # Analyze results
        total_companies = len(results_df)
        successful = len(results_df[results_df['Status'] == 'Success'])
        existing_data = len(results_df[results_df['Status'] == 'Existing data sufficient'])
        total_successful = successful + existing_data
        success_rate = (total_successful / total_companies) * 100
        
        print("\n📊 QUICK TEST RESULTS:")
        print(f"Total Companies: {total_companies}")
        print(f"Successful: {successful}")
        print(f"Existing Data: {existing_data}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        print("\n📋 Detailed Results:")
        for _, row in results_df.iterrows():
            status_emoji = "✅" if row['Status'] == 'Success' else "📋" if 'sufficient' in row['Status'] else "❌"
            phone = "📞" if row.get('ScrapedPhone', 'N/A') != 'N/A' else ""
            email = "📧" if row.get('ScrapedEmail', 'N/A') != 'N/A' else ""
            website = "🌐" if row.get('ScrapedWebsite', 'N/A') != 'N/A' else ""
            
            print(f"{status_emoji} {row['CompanyName'][:30]:<30} {phone}{email}{website} | {row['Status']}")
        
        # Save results
        output_file = f"quick_test_results_{int(time.time())}.xlsx"
        results_df.to_excel(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
        if success_rate >= 80:  # Lower threshold for quick test
            print("🎉 Quick test PASSED! Enhanced scraper is working well.")
            return True
        else:
            print(f"⚠️ Quick test shows room for improvement. Success rate: {success_rate:.1f}%")
            return False
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    exit(0 if success else 1)
