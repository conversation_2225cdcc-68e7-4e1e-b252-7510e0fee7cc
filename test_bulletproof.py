"""
Test the bulletproof scraper strategy
"""

import pandas as pd
from scraper import scrape_contacts
import logging

# Configure logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_bulletproof_strategy():
    """Test the bulletproof strategy with a few companies."""
    print("🚀 Testing Bulletproof Strategy")
    print("=" * 50)
    
    # Create test data with well-known Chinese companies
    test_companies = pd.DataFrame({
        'CompanyName': [
            '华为技术有限公司',
            '腾讯科技有限公司', 
            '阿里巴巴集团控股有限公司'
        ]
    })
    
    print(f"📋 Testing with {len(test_companies)} companies")
    print("🎯 Strategy: Government sources → Accessible directories → Search engines")
    print("🛡️ Protection: Skipping sites that cause 405/419 errors")
    print()
    
    # Run the scraper with bulletproof strategy
    try:
        results = scrape_contacts(test_companies, max_workers=1)  # Use 1 worker for clearer logs
        
        print("📊 Results Summary:")
        print("-" * 30)
        
        for _, row in results.iterrows():
            company = row['CompanyName']
            status = row['Status']
            phone = row.get('ScrapedPhone', 'N/A')
            email = row.get('ScrapedEmail', 'N/A')
            
            print(f"🏢 {company}")
            print(f"   Status: {status}")
            print(f"   Phone: {phone}")
            print(f"   Email: {email}")
            print()
        
        # Calculate success rate
        success_count = len(results[results['Status'] == 'Success'])
        total_count = len(results)
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        
        print(f"✅ Success Rate: {success_rate:.1f}% ({success_count}/{total_count})")
        
        if success_rate >= 50:
            print("🎉 Bulletproof strategy is working!")
        else:
            print("⚠️ Strategy needs improvement")
            
        return results
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return None

if __name__ == "__main__":
    test_bulletproof_strategy()
