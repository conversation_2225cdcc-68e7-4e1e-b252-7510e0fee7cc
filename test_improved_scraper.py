#!/usr/bin/env python3
"""
Test script for the improved Beautiful Soup-based contact scraper.
"""

import pandas as pd
import sys
import os
from scraper import scrape_contacts, ScrapingSession, get_contact_info, extract_from_business_directory
from bs4 import BeautifulSoup
import requests

def test_session_management():
    """Test the ScrapingSession class."""
    print("Testing ScrapingSession...")
    
    session = ScrapingSession()
    
    # Test basic functionality
    try:
        response = session.get('https://httpbin.org/headers')
        print(f"✓ Session GET request successful: {response.status_code}")
        
        # Test user agent rotation
        original_ua = session.session.headers['User-Agent']
        session.rotate_user_agent()
        new_ua = session.session.headers['User-Agent']
        
        if original_ua != new_ua:
            print("✓ User agent rotation working")
        else:
            print("⚠ User agent rotation may not be working")
            
    except Exception as e:
        print(f"✗ Session test failed: {e}")

def test_contact_extraction():
    """Test contact information extraction functions."""
    print("\nTesting contact extraction...")
    
    # Test HTML with contact information
    test_html = """
    <html>
    <body>
        <div class="contact">
            <p>联系电话：021-12345678</p>
            <p>邮箱：<EMAIL></p>
            <p>地址：上海市浦东新区张江高科技园区123号</p>
        </div>
        <div>
            <p>手机：13912345678</p>
            <p>Email: <EMAIL></p>
        </div>
    </body>
    </html>
    """
    
    soup = BeautifulSoup(test_html, 'html.parser')
    result = get_contact_info(soup, 'http://test.com')
    
    print(f"Extracted phone: {result['ScrapedPhone']}")
    print(f"Extracted email: {result['ScrapedEmail']}")
    print(f"Extracted address: {result['ScrapedAddress']}")
    
    # Check if extraction worked
    if result['ScrapedPhone'] != 'N/A':
        print("✓ Phone extraction working")
    else:
        print("⚠ Phone extraction may need improvement")
        
    if result['ScrapedEmail'] != 'N/A':
        print("✓ Email extraction working")
    else:
        print("⚠ Email extraction may need improvement")
        
    if result['ScrapedAddress'] != 'N/A':
        print("✓ Address extraction working")
    else:
        print("⚠ Address extraction may need improvement")

def test_with_sample_data():
    """Test the scraper with sample company data."""
    print("\nTesting with sample data...")
    
    # Create sample DataFrame
    sample_data = pd.DataFrame({
        'CompanyName': ['百度', '阿里巴巴集团'],
        '所属城市': ['北京', '杭州'],
        'ExistingEmail': [None, None],
        'ExistingWebsite': [None, None],
        'LegalRepresentative': [None, None],
        'RegisteredCapital': [None, None],
        'EstablishmentDate': [None, None],
        'ExistingAddress': [None, None]
    })
    
    print(f"Testing with {len(sample_data)} companies:")
    for _, row in sample_data.iterrows():
        print(f"  - {row['CompanyName']}")
    
    try:
        # Test with limited data (only 1 company to avoid overwhelming during testing)
        test_df = sample_data.head(1)
        results = scrape_contacts(test_df, max_workers=1)
        
        print(f"\nResults:")
        for _, result in results.iterrows():
            print(f"Company: {result['CompanyName']}")
            print(f"Status: {result['Status']}")
            print(f"Phone: {result['ScrapedPhone']}")
            print(f"Email: {result['ScrapedEmail']}")
            print(f"Website: {result['ScrapedWebsite']}")
            print("-" * 50)
            
        success_count = len(results[results['Status'] == 'Success'])
        print(f"Success rate: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        
    except Exception as e:
        print(f"✗ Scraping test failed: {e}")
        import traceback
        traceback.print_exc()

def test_existing_data_handling():
    """Test handling of companies with existing contact data."""
    print("\nTesting existing data handling...")
    
    # Create sample with existing data
    existing_data = pd.DataFrame({
        'CompanyName': ['测试公司'],
        'ExistingEmail': ['<EMAIL>'],
        'ExistingWebsite': ['https://www.test.com'],
        'ExistingAddress': ['测试地址'],
        'LegalRepresentative': [None],
        'RegisteredCapital': [None],
        'EstablishmentDate': [None]
    })
    
    try:
        results = scrape_contacts(existing_data, max_workers=1)
        result = results.iloc[0]
        
        if result['Status'] == 'Existing data sufficient':
            print("✓ Existing data handling working correctly")
        else:
            print(f"⚠ Unexpected status: {result['Status']}")
            
    except Exception as e:
        print(f"✗ Existing data test failed: {e}")

def main():
    """Run all tests."""
    print("=" * 60)
    print("IMPROVED CONTACT SCRAPER TEST SUITE")
    print("=" * 60)
    
    # Test individual components
    test_session_management()
    test_contact_extraction()
    test_existing_data_handling()
    
    # Test full scraping (optional - comment out if you don't want to make real requests)
    print("\n" + "=" * 60)
    print("FULL SCRAPING TEST (with real requests)")
    print("=" * 60)
    
    response = input("Run full scraping test? This will make real web requests. (y/N): ")
    if response.lower() == 'y':
        test_with_sample_data()
    else:
        print("Skipping full scraping test.")
    
    print("\n" + "=" * 60)
    print("TEST SUITE COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    main()
