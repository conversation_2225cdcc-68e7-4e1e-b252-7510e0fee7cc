#!/usr/bin/env python3
"""
Comprehensive test to validate 90% success rate for the enhanced scraper with DNS fixes.
"""

import pandas as pd
import time
from scraper import scrape_contacts, robust_china_scraper, setup_dns_for_china
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize DNS configuration
logger.info("🌐 Initializing DNS configuration for Chinese websites...")
dns_success = setup_dns_for_china()
if dns_success:
    logger.info("✅ DNS configuration successful")
else:
    logger.warning("⚠️ DNS configuration failed - may have issues with Chinese websites")

def create_comprehensive_test_data():
    """Create comprehensive test data with various company types."""
    test_companies = [
        # Large well-known companies (should have high success rate)
        {'CompanyName': '阿里巴巴集团控股有限公司', 'City': '杭州', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '腾讯控股有限公司', 'City': '深圳', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '百度在线网络技术（北京）有限公司', 'City': '北京', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '华为技术有限公司', 'City': '深圳', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '小米科技有限责任公司', 'City': '北京', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        
        # Medium-sized companies
        {'CompanyName': '上海浦东发展银行股份有限公司', 'City': '上海', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '中国平安保险（集团）股份有限公司', 'City': '深圳', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '招商银行股份有限公司', 'City': '深圳', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '中国移动通信集团有限公司', 'City': '北京', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '中国石油天然气股份有限公司', 'City': '北京', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        
        # Manufacturing companies
        {'CompanyName': '比亚迪股份有限公司', 'City': '深圳', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '格力电器股份有限公司', 'City': '珠海', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '美的集团股份有限公司', 'City': '佛山', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '海尔智家股份有限公司', 'City': '青岛', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '三一重工股份有限公司', 'City': '长沙', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        
        # Smaller/regional companies (more challenging)
        {'CompanyName': '江苏恒瑞医药股份有限公司', 'City': '连云港', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '山东黄金矿业股份有限公司', 'City': '济南', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '云南白药集团股份有限公司', 'City': '昆明', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '青岛啤酒股份有限公司', 'City': '青岛', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
        {'CompanyName': '五粮液集团有限公司', 'City': '宜宾', 'ExistingEmail': 'N/A', 'ExistingWebsite': 'N/A'},
    ]
    
    return pd.DataFrame(test_companies)

def analyze_results(results_df):
    """Analyze scraping results and calculate success metrics."""
    total_companies = len(results_df)
    
    # Count different types of success
    successful = len(results_df[results_df['Status'] == 'Success'])
    existing_data = len(results_df[results_df['Status'] == 'Existing data sufficient'])
    failed = len(results_df[results_df['Status'].str.contains('failed|No contact', case=False, na=False)])
    
    # Calculate success rate
    total_successful = successful + existing_data
    success_rate = (total_successful / total_companies) * 100
    
    # Detailed analysis
    has_phone = len(results_df[results_df['ScrapedPhone'] != 'N/A'])
    has_email = len(results_df[results_df['ScrapedEmail'] != 'N/A'])
    has_website = len(results_df[results_df['ScrapedWebsite'] != 'N/A'])
    
    print("\n" + "="*60)
    print("🎯 ENHANCED SCRAPER SUCCESS RATE ANALYSIS")
    print("="*60)
    print(f"📊 Total Companies Tested: {total_companies}")
    print(f"✅ Successful Scrapes: {successful}")
    print(f"📋 Existing Data Sufficient: {existing_data}")
    print(f"❌ Failed Scrapes: {failed}")
    print(f"🎯 OVERALL SUCCESS RATE: {success_rate:.1f}%")
    print("\n📈 Contact Information Found:")
    print(f"📞 Phone Numbers: {has_phone} ({has_phone/total_companies*100:.1f}%)")
    print(f"📧 Email Addresses: {has_email} ({has_email/total_companies*100:.1f}%)")
    print(f"🌐 Websites: {has_website} ({has_website/total_companies*100:.1f}%)")
    
    # Success by company type analysis
    print("\n📋 Detailed Results:")
    for _, row in results_df.iterrows():
        status_emoji = "✅" if row['Status'] == 'Success' else "📋" if 'sufficient' in row['Status'] else "❌"
        print(f"{status_emoji} {row['CompanyName'][:30]:<30} | {row['Status']}")
    
    print("="*60)
    
    if success_rate >= 90:
        print("🎉 SUCCESS! Achieved 90%+ success rate target!")
        return True
    else:
        print(f"⚠️  Need improvement. Current: {success_rate:.1f}%, Target: 90%")
        return False

def test_individual_urls():
    """Test individual URLs with the robust scraper to validate DNS fixes."""
    print("\n🧪 Testing individual URLs with robust scraper...")

    test_urls = [
        "https://www.qichacha.com/search?key=阿里巴巴",
        "https://www.tianyancha.com/search?key=腾讯",
        "https://aiqicha.baidu.com/s?q=华为",
        "https://www.baidu.com/s?wd=小米公司官网",
    ]

    for i, url in enumerate(test_urls, 1):
        print(f"\n🔍 Testing URL {i}/{len(test_urls)}: {url}")
        try:
            result = robust_china_scraper(url)
            if result and any(v != 'N/A' for v in result.values() if v != 'error'):
                print(f"✅ Success: Found contact info")
                for key, value in result.items():
                    if value != 'N/A' and key != 'error':
                        print(f"   {key}: {value}")
            else:
                print(f"❌ Failed: No contact info found")
                if 'error' in result:
                    print(f"   Error: {result['error']}")
        except Exception as e:
            print(f"❌ Exception: {e}")

def main():
    """Run comprehensive test for 90% success rate validation."""
    print("🚀 Starting Enhanced Scraper 90% Success Rate Test with DNS Fixes...")

    # First test individual URLs to validate DNS fixes
    test_individual_urls()

    # Create test data
    test_df = create_comprehensive_test_data()
    print(f"\n📋 Created test dataset with {len(test_df)} companies")

    # Record start time
    start_time = time.time()

    try:
        # Run enhanced scraper with optimized settings
        print("🔍 Running enhanced scraper with all optimizations...")
        results_df = scrape_contacts(test_df, max_workers=2)  # More conservative for testing

        # Record end time
        end_time = time.time()
        duration = end_time - start_time

        print(f"⏱️  Scraping completed in {duration:.1f} seconds")
        print(f"⚡ Average time per company: {duration/len(test_df):.1f} seconds")

        # Analyze results
        success = analyze_results(results_df)

        # Save detailed results
        output_file = f"enhanced_scraper_test_results_{int(time.time())}.xlsx"
        results_df.to_excel(output_file, index=False)
        print(f"💾 Detailed results saved to: {output_file}")

        return success

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
