"""
Enhanced Contact Scraper with Bright Data Integration
Combines the existing scraper functionality with Bright Data proxy services for improved success rates.
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import random
import re
import logging
from typing import Dict, List, Optional, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import os
from urllib.parse import urlparse, quote, quote_plus
import chardet

# Import Bright Data configuration
from bright_data_config import (
    bright_data_config,
    get_session_with_bright_data_proxy,
    create_bright_data_playwright_context,
    get_bright_data_headers
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('enhanced_scraper.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class EnhancedScrapingSession:
    """Enhanced scraping session with Bright Data proxy integration."""
    
    def __init__(self, use_bright_data: bool = True):
        self.use_bright_data = use_bright_data and bright_data_config.has_proxy_config
        self.session = None
        self.request_count = 0
        self.max_requests_per_session = 50
        self.failed_requests = 0
        self.max_failed_requests = 5
        self.last_request_time = 0
        self.min_delay = 2.0
        self.max_delay = 5.0
        
        self._create_session()
        
        if self.use_bright_data:
            logger.info("✅ Enhanced scraping session with Bright Data proxy")
        else:
            logger.info("⚠️ Enhanced scraping session without proxy (Bright Data not configured)")
    
    def _create_session(self):
        """Create a new session with appropriate configuration."""
        if self.use_bright_data:
            self.session = get_session_with_bright_data_proxy()
        else:
            # Fallback to regular session with enhanced headers
            self.session = requests.Session()
            self.session.headers.update(get_bright_data_headers())
            
            # Add retry strategy
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry
            
            retry_strategy = Retry(
                total=3,
                backoff_factor=2,
                status_forcelist=[429, 500, 502, 503, 504, 403, 408],
                allowed_methods=["HEAD", "GET", "OPTIONS"]
            )
            
            adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=20, pool_maxsize=20)
            self.session.mount("http://", adapter)
            self.session.mount("https://", adapter)
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """Enhanced GET request with rate limiting and session management."""
        self._rate_limit()
        
        # Check if session needs refresh
        if (self.request_count >= self.max_requests_per_session or 
            self.failed_requests >= self.max_failed_requests):
            logger.info("Refreshing session due to limits")
            self._refresh_session()
        
        try:
            # Set default timeout
            if 'timeout' not in kwargs:
                kwargs['timeout'] = 45
            
            # Rotate headers occasionally
            if random.random() < 0.3:
                self.session.headers.update(get_bright_data_headers())
            
            self.request_count += 1
            response = self.session.get(url, **kwargs)
            
            # Check for blocking indicators
            if self._is_blocked_response(response):
                logger.warning(f"Detected blocking for {url}, refreshing session")
                self._refresh_session()
                raise requests.RequestException("Response appears to be blocked")
            
            response.raise_for_status()
            self.failed_requests = 0
            return response
            
        except requests.RequestException as e:
            self.failed_requests += 1
            logger.warning(f"Request failed for {url} (attempt {self.failed_requests}): {e}")
            raise
    
    def _rate_limit(self):
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        delay = random.uniform(self.min_delay, self.max_delay)
        
        if time_since_last < delay:
            sleep_time = delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _is_blocked_response(self, response: requests.Response) -> bool:
        """Check if response indicates blocking or captcha."""
        if response.status_code in [403, 429, 405, 419]:
            return True
        
        content_lower = response.text.lower()
        blocking_indicators = [
            '验证码', 'captcha', '人机验证', '访问频繁',
            'access denied', 'blocked', '请稍后再试',
            'robot', '机器人', 'security check'
        ]
        
        return any(indicator in content_lower for indicator in blocking_indicators)
    
    def _refresh_session(self):
        """Refresh session to avoid detection."""
        if self.session:
            self.session.close()
        self.request_count = 0
        self.failed_requests = 0
        self._create_session()
        logger.info("Session refreshed successfully")

def extract_chinese_contacts_enhanced(text: str) -> Dict[str, set]:
    """Enhanced Chinese contact extraction with improved patterns."""
    contacts = {"emails": set(), "phones": set(), "wechats": set(), "qqs": set()}
    
    # Enhanced phone patterns for Chinese numbers
    phone_patterns = [
        r'1[3-9]\d{9}',  # Mobile phones
        r'0\d{2,3}-?\d{7,8}',  # Landlines
        r'\(\d{3,4}\)\d{7,8}',  # Landlines with parentheses
        r'400-?\d{3}-?\d{4}',  # 400 service numbers
        r'800-?\d{3}-?\d{4}',  # 800 service numbers
        r'\+86\s?1[3-9]\d{9}',  # International mobile
        r'(\+?86\s?)?1[3-9]\d{9}',  # Mobile with optional +86
    ]
    
    # WeChat patterns
    wechat_patterns = [
        r'微信[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
        r'WeChat[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
        r'微信号[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
        r'wx[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
    ]
    
    # QQ patterns
    qq_patterns = [
        r'QQ[：:\s]*([1-9][0-9]{4,10})',
        r'qq[：:\s]*([1-9][0-9]{4,10})',
        r'企鹅[：:\s]*([1-9][0-9]{4,10})',
    ]
    
    # Email patterns
    email_patterns = [
        r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
        r'邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        r'Email[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        r'mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
    ]
    
    # Extract phones
    for pattern in phone_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            phone = re.sub(r'[-\s+()（）]', '', str(match))
            if phone.startswith('86') and len(phone) > 11:
                phone = phone[2:]
            elif phone.startswith('+86'):
                phone = phone[3:]
            
            if len(phone) >= 7 and phone.isdigit():
                if ((len(phone) == 11 and phone.startswith('1')) or 
                    (len(phone) >= 10 and phone.startswith('0')) or 
                    (len(phone) >= 10 and phone.startswith(('400', '800')))):
                    contacts["phones"].add(phone)
    
    # Extract WeChat IDs
    for pattern in wechat_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        contacts["wechats"].update(matches)
    
    # Extract QQ numbers
    for pattern in qq_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        contacts["qqs"].update(matches)
    
    # Extract emails
    for pattern in email_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                email = match[0] if match[0] else match[1] if len(match) > 1 else ''
            else:
                email = match
            
            email = str(email).strip()
            if '@' in email and '.' in email and len(email) > 5:
                skip_domains = ['example.com', 'test.com', 'placeholder', 'noreply', 'dummy']
                if not any(skip in email.lower() for skip in skip_domains):
                    contacts["emails"].add(email)
    
    return contacts

def generate_enhanced_search_urls(company_name: str, city: Optional[str] = None) -> List[str]:
    """Generate enhanced search URLs with focus on accessible sources."""
    encoded_name = quote(company_name)
    encoded_name_plus = quote_plus(company_name)
    
    urls = []
    
    # Government and official sources (most reliable)
    urls.extend([
        f'https://www.gsxt.gov.cn/corp-query-search-1.html?keyword={encoded_name}',
        f'https://www.creditchina.gov.cn/search?keyword={encoded_name}',
    ])
    
    # Accessible business directories
    urls.extend([
        f'https://www.huangye88.com/search/{encoded_name}',
        f'https://www.b2b.cn/search/{encoded_name}',
        f'https://www.made-in-china.com/search?word={encoded_name}',
        f'https://www.1688.com/company/company_search.htm?keywords={encoded_name}',
        f'https://www.11467.com/search.php?kw={encoded_name}',
    ])
    
    # Search engines for indirect discovery
    search_queries = [
        f'{company_name} 联系方式',
        f'{company_name} 官网',
        f'{company_name} 电话 邮箱',
    ]
    
    if city:
        search_queries.append(f'{company_name} {city} 联系方式')
    
    for query in search_queries[:2]:  # Limit to avoid detection
        encoded_query = quote(query)
        urls.extend([
            f'https://www.baidu.com/s?wd={encoded_query}',
            f'https://www.sogou.com/web?query={encoded_query}',
        ])
    
    return urls[:10]  # Return top 10 URLs

def scrape_single_company_enhanced(session: EnhancedScrapingSession, company_data: Dict) -> Dict[str, str]:
    """Enhanced single company scraping with Bright Data integration."""
    company_name = str(company_data['CompanyName'])

    result = {
        'CompanyName': company_name,
        'ExistingEmail': company_data.get('ExistingEmail', 'N/A'),
        'ExistingWebsite': company_data.get('ExistingWebsite', 'N/A'),
        'ExistingAddress': company_data.get('ExistingAddress', 'N/A'),
        'LegalRepresentative': company_data.get('LegalRepresentative', 'N/A'),
        'RegisteredCapital': company_data.get('RegisteredCapital', 'N/A'),
        'EstablishmentDate': company_data.get('EstablishmentDate', 'N/A'),
        'ScrapedPhone': 'N/A',
        'ScrapedEmail': 'N/A',
        'ScrapedAddress': 'N/A',
        'ScrapedWebsite': 'N/A',
        'Status': 'Failed',
        'ErrorDetails': None
    }

    try:
        logger.info(f"🚀 Enhanced scraping for {company_name} with Bright Data")

        # Check if we already have sufficient data
        has_email = pd.notna(result['ExistingEmail']) and result['ExistingEmail'] not in ['-', '', 'N/A']
        has_website = pd.notna(result['ExistingWebsite']) and result['ExistingWebsite'] not in ['-', '', 'N/A']

        if has_email and has_website:
            result['Status'] = 'Existing data sufficient'
            logger.info(f"Skipping {company_name} - already has email and website")
            return result

        # Generate target URLs
        city = company_data.get('City') or company_data.get('所属城市')
        target_urls = generate_enhanced_search_urls(company_name, city)

        if has_website:
            target_urls.insert(0, result['ExistingWebsite'])

        logger.info(f"Generated {len(target_urls)} target URLs for {company_name}")

        best_result = None
        best_contact_count = 0

        # Try each URL with enhanced error handling
        for i, url in enumerate(target_urls[:6]):  # Limit to 6 URLs for efficiency
            try:
                logger.info(f"🔍 Visiting URL {i+1}/{min(len(target_urls), 6)}: {url[:80]}...")

                # Enhanced retry logic
                max_retries = 2
                response = None

                for retry in range(max_retries):
                    try:
                        if retry > 0:
                            delay = 3 + random.uniform(1, 3)
                            logger.info(f"⏳ Retry {retry}/{max_retries} after {delay:.1f}s delay")
                            time.sleep(delay)

                        response = session.get(url, timeout=45)

                        if response.status_code == 200:
                            break
                        elif response.status_code in [405, 419, 403, 429]:
                            logger.warning(f"❌ Access denied (status: {response.status_code}) - skipping {url}")
                            break
                        else:
                            logger.warning(f"⚠️ Status {response.status_code} for {url}")
                            if retry == max_retries - 1:
                                break
                            continue

                    except Exception as e:
                        if retry == max_retries - 1:
                            logger.error(f"❌ All retries failed for {url}: {e}")
                            break
                        logger.warning(f"Retry {retry + 1} for {url}: {e}")
                        time.sleep(3)

                if not response or response.status_code != 200:
                    logger.warning(f"Failed to access {url}")
                    continue

                # Enhanced encoding detection
                content = None
                try:
                    if hasattr(response, 'encoding') and response.encoding:
                        content = response.content.decode(response.encoding, errors='ignore')
                    else:
                        detected = chardet.detect(response.content)
                        encoding = detected.get('encoding', 'utf-8')
                        content = response.content.decode(encoding, errors='ignore')
                except:
                    content = response.content.decode('utf-8', errors='ignore')

                soup = BeautifulSoup(content, 'html.parser')
                result['ScrapedWebsite'] = url

                # Extract contact information
                page_text = soup.get_text()
                extracted_contacts = extract_chinese_contacts_enhanced(page_text)

                # Format results
                scraped_data = {
                    'ScrapedPhone': 'N/A',
                    'ScrapedEmail': 'N/A',
                    'ScrapedAddress': 'N/A'
                }

                # Process phones
                if extracted_contacts['phones']:
                    scraped_data['ScrapedPhone'] = ', '.join(sorted(extracted_contacts['phones']))

                # Process emails
                if extracted_contacts['emails']:
                    scraped_data['ScrapedEmail'] = ', '.join(sorted(extracted_contacts['emails']))

                # Add WeChat and QQ to phone field
                additional_contacts = []
                if extracted_contacts['wechats']:
                    wechat_list = list(extracted_contacts['wechats'])[:2]
                    additional_contacts.extend([f"微信:{wc}" for wc in wechat_list])

                if extracted_contacts['qqs']:
                    qq_list = list(extracted_contacts['qqs'])[:2]
                    additional_contacts.extend([f"QQ:{qq}" for qq in qq_list])

                if additional_contacts:
                    if scraped_data['ScrapedPhone'] != 'N/A':
                        scraped_data['ScrapedPhone'] += ', ' + ', '.join(additional_contacts)
                    else:
                        scraped_data['ScrapedPhone'] = ', '.join(additional_contacts)

                # Extract address
                address_patterns = [
                    r'地址[：:\s]*([^\n]+)',
                    r'注册地址[：:\s]*([^\n]+)',
                    r'办公地址[：:\s]*([^\n]+)',
                    r'联系地址[：:\s]*([^\n]+)',
                ]

                for pattern in address_patterns:
                    matches = re.findall(pattern, page_text)
                    for match in matches:
                        address = str(match).strip()
                        if (len(address) > 10 and
                            any(c in address for c in ['市', '区', '路', '街', '号', '县', '省'])):
                            scraped_data['ScrapedAddress'] = address[:200]
                            break
                    if scraped_data['ScrapedAddress'] != 'N/A':
                        break

                # Log findings
                found_items = [k for k, v in scraped_data.items() if v != 'N/A']
                if found_items:
                    logger.info(f"✅ Found contact info: {', '.join(found_items)}")
                    for k, v in scraped_data.items():
                        if v != 'N/A':
                            logger.info(f"  {k}: {v[:100]}..." if len(str(v)) > 100 else f"  {k}: {v}")

                # Update result
                current_result = result.copy()
                current_result.update(scraped_data)

                contact_count = sum(1 for v in scraped_data.values() if v != 'N/A')

                if contact_count > best_contact_count:
                    best_result = current_result.copy()
                    best_contact_count = contact_count
                    logger.info(f"New best result with {contact_count} contact items")

                # Stop if we have excellent results
                if contact_count >= 2:
                    logger.info(f"Excellent results found ({contact_count} items), stopping search")
                    break

            except Exception as e:
                logger.warning(f"Failed to scrape {url}: {e}")
                continue

        # Determine success
        if best_result:
            has_phone = best_result.get('ScrapedPhone', 'N/A') != 'N/A'
            has_email = best_result.get('ScrapedEmail', 'N/A') != 'N/A'
            has_website = best_result.get('ScrapedWebsite', 'N/A') != 'N/A'
            has_existing_data = (
                best_result.get('ExistingEmail', 'N/A') != 'N/A' or
                best_result.get('ExistingWebsite', 'N/A') != 'N/A'
            )

            if has_phone or has_email or has_website or has_existing_data or best_contact_count > 0:
                best_result['Status'] = 'Success'
                success_details = []
                if has_phone: success_details.append("phone")
                if has_email: success_details.append("email")
                if has_website: success_details.append("website")
                if has_existing_data: success_details.append("existing_data")

                logger.info(f"✅ Successfully scraped {company_name} with: {', '.join(success_details)}")
                return best_result

        result['Status'] = 'No contact information found'
        logger.warning(f"❌ No contact information found for {company_name}")
        return result

    except Exception as e:
        logger.error(f"Enhanced scraping failed for {company_name}: {e}")
        result['Status'] = 'Scraping failed'
        result['ErrorDetails'] = str(e)
        return result

def scrape_contacts_enhanced(df: pd.DataFrame, max_workers: int = 3, use_bright_data: bool = True) -> pd.DataFrame:
    """Enhanced contact scraping with Bright Data integration."""
    results = []
    companies = df.to_dict('records')

    logger.info(f"🚀 Starting enhanced scraping for {len(companies)} companies")
    logger.info(f"Bright Data integration: {'✅ Enabled' if use_bright_data else '❌ Disabled'}")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Create sessions for each worker
        sessions = [EnhancedScrapingSession(use_bright_data=use_bright_data) for _ in range(max_workers)]
        session_index = 0

        # Submit tasks
        future_to_company = {}
        for company in companies:
            session = sessions[session_index % len(sessions)]
            future = executor.submit(scrape_single_company_enhanced, session, company)
            future_to_company[future] = company['CompanyName']
            session_index += 1

        # Collect results
        for future in as_completed(future_to_company):
            company_name = future_to_company[future]
            try:
                result = future.result()
                results.append(result)
                logger.info(f"Completed scraping for {company_name} - Status: {result['Status']}")
            except Exception as e:
                logger.error(f"Exception occurred for {company_name}: {e}")
                failed_result = {
                    'CompanyName': company_name,
                    'Status': 'Exception occurred',
                    'ErrorDetails': str(e)
                }
                results.append(failed_result)

    logger.info(f"Enhanced scraping completed. Processed {len(results)} companies")
    return pd.DataFrame(results)

def main():
    """Main execution function."""
    input_file = 'data/查询企业名单20250819 - 副本.xlsx'
    output_file = 'data/enhanced_scraped_contacts.xlsx'

    if not os.path.exists(input_file):
        logger.error(f"Input file '{input_file}' not found.")
        return

    try:
        df = pd.read_excel(input_file)
        logger.info(f"Loaded {len(df)} companies from {input_file}")
    except Exception as e:
        logger.error(f"Failed to read {input_file}: {e}")
        return

    # Map Chinese column names
    column_mapping = {
        '企业名称': 'CompanyName',
        '邮箱': 'ExistingEmail',
        '网址': 'ExistingWebsite',
        '企业地址': 'ExistingAddress',
        '法定代表人': 'LegalRepresentative',
        '注册资本': 'RegisteredCapital',
        '成立日期': 'EstablishmentDate',
        '所属城市': 'City'
    }

    existing_mappings = {k: v for k, v in column_mapping.items() if k in df.columns}
    df = df.rename(columns=existing_mappings)

    logger.info(f"Mapped {len(existing_mappings)} columns: {list(existing_mappings.keys())}")

    # Start enhanced scraping
    try:
        scraped_df = scrape_contacts_enhanced(df, max_workers=3, use_bright_data=True)

        # Save results
        scraped_df.to_excel(output_file, index=False)
        logger.info(f"Enhanced scraping complete. Results saved to {output_file}")

        # Print summary
        success_count = len(scraped_df[scraped_df['Status'] == 'Success'])
        total_count = len(scraped_df)
        logger.info(f"Success rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")

    except Exception as e:
        logger.error(f"Enhanced scraping failed: {e}")

if __name__ == "__main__":
    main()
