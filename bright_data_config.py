"""
Bright Data Integration Configuration
Handles proxy configuration and API integration for improved scraping success rates.
"""

import os
import random
import logging
from typing import Dict, Optional, Tuple, List
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class BrightDataConfig:
    """Configuration class for Bright Data proxy services."""
    
    def __init__(self):
        self.api_key = os.getenv('BRIGHT_DATA_API_KEY')
        
        # Datacenter Proxy Configuration
        self.datacenter_host = os.getenv('BRIGHT_DATA_PROXY_HOST')
        self.datacenter_port = int(os.getenv('BRIGHT_DATA_PROXY_PORT', 22225))
        self.datacenter_username = os.getenv('BRIGHT_DATA_USERNAME')
        self.datacenter_password = os.getenv('BRIGHT_DATA_PASSWORD')
        
        # Residential Proxy Configuration
        self.residential_host = os.getenv('BRIGHT_DATA_RESIDENTIAL_HOST')
        self.residential_port = int(os.getenv('BRIGHT_DATA_RESIDENTIAL_PORT', 22225))
        self.residential_username = os.getenv('BRIGHT_DATA_RESIDENTIAL_USERNAME')
        self.residential_password = os.getenv('BRIGHT_DATA_RESIDENTIAL_PASSWORD')
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate that required configuration is present."""
        if not self.api_key:
            logger.warning("BRIGHT_DATA_API_KEY not found in environment variables")
        
        # Check if at least one proxy configuration is complete
        datacenter_complete = all([
            self.datacenter_host, self.datacenter_username, self.datacenter_password
        ])
        
        residential_complete = all([
            self.residential_host, self.residential_username, self.residential_password
        ])
        
        if not (datacenter_complete or residential_complete):
            logger.warning("No complete Bright Data proxy configuration found")
            self.has_proxy_config = False
        else:
            self.has_proxy_config = True
            logger.info("✅ Bright Data proxy configuration loaded")
    
    def get_datacenter_proxy(self) -> Optional[Dict[str, str]]:
        """Get datacenter proxy configuration."""
        if not all([self.datacenter_host, self.datacenter_username, self.datacenter_password]):
            return None
        
        return {
            'http': f'http://{self.datacenter_username}:{self.datacenter_password}@{self.datacenter_host}:{self.datacenter_port}',
            'https': f'http://{self.datacenter_username}:{self.datacenter_password}@{self.datacenter_host}:{self.datacenter_port}'
        }
    
    def get_residential_proxy(self) -> Optional[Dict[str, str]]:
        """Get residential proxy configuration."""
        if not all([self.residential_host, self.residential_username, self.residential_password]):
            return None
        
        return {
            'http': f'http://{self.residential_username}:{self.residential_password}@{self.residential_host}:{self.residential_port}',
            'https': f'http://{self.residential_username}:{self.residential_password}@{self.residential_host}:{self.residential_port}'
        }
    
    def get_random_proxy(self) -> Optional[Dict[str, str]]:
        """Get a random proxy configuration for load balancing."""
        available_proxies = []
        
        datacenter = self.get_datacenter_proxy()
        if datacenter:
            available_proxies.append(('datacenter', datacenter))
        
        residential = self.get_residential_proxy()
        if residential:
            available_proxies.append(('residential', residential))
        
        if not available_proxies:
            return None
        
        proxy_type, proxy_config = random.choice(available_proxies)
        logger.debug(f"Selected {proxy_type} proxy")
        return proxy_config
    
    def get_playwright_proxy(self, proxy_type: str = 'random') -> Optional[Dict[str, str]]:
        """Get proxy configuration formatted for Playwright."""
        if proxy_type == 'datacenter':
            proxy_config = self.get_datacenter_proxy()
        elif proxy_type == 'residential':
            proxy_config = self.get_residential_proxy()
        else:  # random
            proxy_config = self.get_random_proxy()
        
        if not proxy_config:
            return None
        
        # Extract proxy details for Playwright format
        proxy_url = proxy_config['http']
        # Parse: *****************************:port
        if '://' in proxy_url:
            protocol, rest = proxy_url.split('://', 1)
            if '@' in rest:
                auth, server = rest.split('@', 1)
                username, password = auth.split(':', 1)
                
                return {
                    'server': f'http://{server}',
                    'username': username,
                    'password': password
                }
        
        return None

# Global configuration instance
bright_data_config = BrightDataConfig()

def get_bright_data_headers() -> Dict[str, str]:
    """Get enhanced headers optimized for Chinese websites with Bright Data."""
    chinese_user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.76",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0"
    ]
    
    return {
        'User-Agent': random.choice(chinese_user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'DNT': '1',
        'Referer': 'https://www.baidu.com/'
    }

def get_session_with_bright_data_proxy():
    """Create a requests session with Bright Data proxy configuration."""
    import requests
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    
    session = requests.Session()
    
    # Configure proxy
    proxy_config = bright_data_config.get_random_proxy()
    if proxy_config:
        session.proxies.update(proxy_config)
        logger.info("✅ Session configured with Bright Data proxy")
    else:
        logger.warning("⚠️ No Bright Data proxy available, using direct connection")
    
    # Enhanced headers
    session.headers.update(get_bright_data_headers())
    
    # Retry strategy optimized for Chinese websites
    retry_strategy = Retry(
        total=3,
        backoff_factor=2,
        status_forcelist=[429, 500, 502, 503, 504, 403, 408, 520, 521, 522, 524],
        allowed_methods=["HEAD", "GET", "OPTIONS"]
    )
    
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=20,
        pool_maxsize=20
    )
    
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session

def create_bright_data_playwright_context(playwright_instance):
    """Create a Playwright browser context with Bright Data proxy."""
    proxy_config = bright_data_config.get_playwright_proxy()
    
    browser_args = [
        '--disable-blink-features=AutomationControlled',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',  # Faster loading
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
    ]
    
    browser = playwright_instance.chromium.launch(
        headless=True,
        proxy=proxy_config,
        args=browser_args,
        timeout=60000
    )
    
    # Enhanced headers for Chinese websites
    headers = get_bright_data_headers()
    
    context = browser.new_context(
        viewport={"width": 1920, "height": 1080},
        user_agent=headers['User-Agent'],
        locale="zh-CN",
        timezone_id="Asia/Shanghai",
        geolocation={"longitude": 116.3683244, "latitude": 39.915085},  # Beijing
        permissions=["geolocation"],
        extra_http_headers=headers
    )
    
    # Anti-detection scripts
    context.add_init_script("""
        // Override webdriver detection
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
        
        // Override plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5]
        });
        
        // Override languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en-US', 'en']
        });
        
        // Override chrome detection
        window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
        };
        
        // Override permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
    """)
    
    if proxy_config:
        logger.info("✅ Playwright context configured with Bright Data proxy")
    else:
        logger.warning("⚠️ Playwright context created without proxy")
    
    return browser, context

# Export configuration for easy access
__all__ = [
    'BrightDataConfig',
    'bright_data_config',
    'get_bright_data_headers',
    'get_session_with_bright_data_proxy',
    'create_bright_data_playwright_context'
]
