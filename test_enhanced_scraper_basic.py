"""
Basic test of the enhanced scraper functionality without requiring Bright Data proxies.
This test demonstrates the enhanced scraper working in fallback mode.
"""

import pandas as pd
import logging
from enhanced_scraper_with_bright_data import (
    EnhancedScrapingSession,
    extract_chinese_contacts_enhanced,
    scrape_single_company_enhanced
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_contact_extraction():
    """Test the enhanced contact extraction functionality."""
    logger.info("🧪 Testing enhanced contact extraction...")
    
    # Test text with various Chinese contact formats
    test_text = """
    公司名称：北京科技创新有限公司
    联系电话：13812345678、010-12345678
    客服热线：************
    传真：021-87654321
    邮箱：<EMAIL>、<EMAIL>
    企业邮箱：<EMAIL>
    微信号：bjtech2023
    QQ客服：*********
    公司地址：北京市海淀区中关村大街123号科技大厦15层
    注册地址：北京市朝阳区建国门外大街456号
    """
    
    contacts = extract_chinese_contacts_enhanced(test_text)
    
    logger.info("✅ Enhanced contact extraction results:")
    logger.info(f"  📱 Phones: {len(contacts['phones'])} found")
    for phone in sorted(contacts['phones']):
        logger.info(f"    - {phone}")
    
    logger.info(f"  📧 Emails: {len(contacts['emails'])} found")
    for email in sorted(contacts['emails']):
        logger.info(f"    - {email}")
    
    logger.info(f"  💬 WeChat: {len(contacts['wechats'])} found")
    for wechat in contacts['wechats']:
        logger.info(f"    - {wechat}")
    
    logger.info(f"  🐧 QQ: {len(contacts['qqs'])} found")
    for qq in contacts['qqs']:
        logger.info(f"    - {qq}")
    
    # Verify we found expected contacts
    expected_results = {
        'phones': ['13812345678', '01012345678', '40012345678'],
        'emails': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'wechats': ['bjtech2023'],
        'qqs': ['*********']
    }
    
    success = True
    for contact_type, expected_list in expected_results.items():
        found = contacts[contact_type]
        for expected in expected_list:
            if expected in found:
                logger.info(f"    ✅ Found expected {contact_type}: {expected}")
            else:
                logger.warning(f"    ❌ Missing expected {contact_type}: {expected}")
                success = False
    
    return success

def test_session_creation():
    """Test creating enhanced sessions with and without Bright Data."""
    logger.info("🧪 Testing enhanced session creation...")
    
    try:
        # Test with Bright Data (should fall back to regular session if not configured)
        session_with_bd = EnhancedScrapingSession(use_bright_data=True)
        logger.info("✅ Enhanced session with Bright Data flag created")
        
        # Test without Bright Data
        session_without_bd = EnhancedScrapingSession(use_bright_data=False)
        logger.info("✅ Enhanced session without Bright Data created")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Session creation failed: {e}")
        return False

def test_single_company_scraping():
    """Test scraping a single company with the enhanced scraper."""
    logger.info("🧪 Testing single company scraping...")
    
    # Create test company data
    test_company = {
        'CompanyName': '测试科技有限公司',
        'ExistingEmail': 'N/A',
        'ExistingWebsite': 'https://httpbin.org/html',  # Use a reliable test site
        'ExistingAddress': 'N/A',
        'LegalRepresentative': '张三',
        'RegisteredCapital': '1000万',
        'EstablishmentDate': '2020-01-01',
        'City': '北京'
    }
    
    try:
        # Create session without Bright Data for testing
        session = EnhancedScrapingSession(use_bright_data=False)
        
        # Test scraping
        result = scrape_single_company_enhanced(session, test_company)
        
        logger.info("✅ Single company scraping completed")
        logger.info(f"  Company: {result['CompanyName']}")
        logger.info(f"  Status: {result['Status']}")
        logger.info(f"  Scraped Phone: {result['ScrapedPhone']}")
        logger.info(f"  Scraped Email: {result['ScrapedEmail']}")
        logger.info(f"  Scraped Website: {result['ScrapedWebsite']}")
        
        return result['Status'] != 'Failed'
        
    except Exception as e:
        logger.error(f"❌ Single company scraping failed: {e}")
        return False

def test_batch_scraping():
    """Test batch scraping with multiple companies."""
    logger.info("🧪 Testing batch scraping...")
    
    # Create test data
    test_companies = [
        {
            'CompanyName': '北京测试科技有限公司',
            'ExistingEmail': '<EMAIL>',
            'ExistingWebsite': 'N/A',
            'City': '北京'
        },
        {
            'CompanyName': '上海创新企业有限公司',
            'ExistingEmail': 'N/A',
            'ExistingWebsite': 'https://httpbin.org/html',
            'City': '上海'
        },
        {
            'CompanyName': '深圳科技发展有限公司',
            'ExistingEmail': 'N/A',
            'ExistingWebsite': 'N/A',
            'City': '深圳'
        }
    ]
    
    try:
        from enhanced_scraper_with_bright_data import scrape_contacts_enhanced
        
        df = pd.DataFrame(test_companies)
        
        # Run batch scraping without Bright Data for testing
        results = scrape_contacts_enhanced(df, max_workers=2, use_bright_data=False)
        
        logger.info("✅ Batch scraping completed")
        logger.info(f"  Total companies processed: {len(results)}")
        
        # Show results summary
        status_counts = results['Status'].value_counts()
        for status, count in status_counts.items():
            logger.info(f"  {status}: {count}")
        
        return len(results) == len(test_companies)
        
    except Exception as e:
        logger.error(f"❌ Batch scraping failed: {e}")
        return False

def main():
    """Run all basic tests."""
    logger.info("🚀 Starting Enhanced Scraper Basic Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Contact Extraction", test_contact_extraction),
        ("Session Creation", test_session_creation),
        ("Single Company Scraping", test_single_company_scraping),
        ("Batch Scraping", test_batch_scraping),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.warning(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All basic tests passed! Enhanced scraper is working correctly.")
        logger.info("💡 To use Bright Data proxies, configure your credentials in .env file")
    else:
        logger.warning("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
